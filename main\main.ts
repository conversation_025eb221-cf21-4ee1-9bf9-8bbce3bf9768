import { app, BrowserWindow, ipcMain, desktopCapturer, dialog } from "electron";
import path from "node:path";
import fs from "node:fs";
import started from "electron-squirrel-startup";

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (started) {
  app.quit();
}

const createWindow = () => {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, "preload.js"),
    },
  });

  // and load the index.html of the app.
  if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
  } else {
    mainWindow.loadFile(
      path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`)
    );
  }

  // Open the DevTools.
  mainWindow.webContents.openDevTools();
};

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on("ready", createWindow);

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("activate", () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and import them here.

// 创建截图窗口
let captureWindow: BrowserWindow | null = null;

// 处理截图请求
ipcMain.handle("start-screen-capture", async () => {
  try {
    // 获取所有可用的屏幕源
    const sources = await desktopCapturer.getSources({
      types: ["screen"],
      thumbnailSize: { width: 3840, height: 2160 }, // 更高分辨率以支持高清屏幕
    });

    if (sources.length === 0) {
      throw new Error("没有找到可用的屏幕源");
    }

    // 获取主显示器
    const primaryDisplay =
      sources.find((source) => source.id.includes("screen:0")) || sources[0];

    // 获取屏幕尺寸
    const size = primaryDisplay.thumbnail.getSize();
    const width = size.width;
    const height = size.height;

    // 隐藏主窗口
    const mainWindow = BrowserWindow.getAllWindows().find(
      (w) => w !== captureWindow
    );
    if (mainWindow) {
      mainWindow.hide();
    }

    // 创建截图窗口
    if (!captureWindow) {
      captureWindow = new BrowserWindow({
        width: width,
        height: height,
        frame: false,
        transparent: true,
        show: false,
        alwaysOnTop: true,
        skipTaskbar: true,
        fullscreen: true,
        webPreferences: {
          preload: path.join(__dirname, "preload.js"),
          nodeIntegration: false,
          contextIsolation: true,
        },
      });

      // 加载截图界面
      if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
        captureWindow.loadURL(`${MAIN_WINDOW_VITE_DEV_SERVER_URL}#/capture`);
      } else {
        captureWindow.loadFile(
          path.join(
            __dirname,
            `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`
          ),
          { hash: "capture" }
        );
      }

      captureWindow.on("closed", () => {
        captureWindow = null;
      });
    }

    // 确保窗口尺寸与屏幕匹配
    captureWindow.setSize(width, height);

    // 等待页面加载完成
    captureWindow.once("ready-to-show", () => {
      // 显示截图窗口
      captureWindow.show();
      captureWindow.focus();
      captureWindow.setFullScreen(true);

      // 将截图数据传递给渲染进程
      captureWindow.webContents.send("capture-screen-data", {
        thumbnail: primaryDisplay.thumbnail.toDataURL(),
        id: primaryDisplay.id,
        name: primaryDisplay.name,
        display_id: primaryDisplay.display_id,
        width: width,
        height: height,
      });
    });

    // 如果窗口已经准备好了，直接发送数据
    if (captureWindow.isVisible()) {
      captureWindow.webContents.send("capture-screen-data", {
        thumbnail: primaryDisplay.thumbnail.toDataURL(),
        id: primaryDisplay.id,
        name: primaryDisplay.name,
        display_id: primaryDisplay.display_id,
        width: width,
        height: height,
      });
    }
  } catch (error) {
    console.error("截图失败:", error);
    dialog.showErrorBox("截图失败", `无法捕获屏幕: ${error.message}`);
  }
});

// 保存截图
ipcMain.handle("save-screen-capture", async (_event, dataUrl: string) => {
  try {
    // 移除 data:image/png;base64, 前缀
    const base64Data = dataUrl.replace(/^data:image\/\w+;base64,/, "");
    const buffer = Buffer.from(base64Data, "base64");

    // 打开保存对话框
    const { filePath } = await dialog.showSaveDialog({
      title: "保存截图",
      defaultPath: path.join(
        app.getPath("pictures"),
        `截图_${new Date().toISOString().replace(/:/g, "-")}.png`
      ),
      filters: [{ name: "图片", extensions: ["png"] }],
    });

    if (!filePath) {
      return null; // 用户取消了保存
    }

    // 写入文件
    fs.writeFileSync(filePath, buffer);

    // 关闭截图窗口
    if (captureWindow) {
      captureWindow.hide();
    }

    // 通知渲染进程截图已完成
    const mainWindow = BrowserWindow.getAllWindows().find(
      (w) => w !== captureWindow
    );
    if (mainWindow) {
      mainWindow.webContents.send("screen-capture-complete", filePath);
    }

    return filePath;
  } catch (error) {
    console.error("保存截图失败:", error);
    dialog.showErrorBox("保存失败", `无法保存截图: ${error.message}`);
    return null;
  } finally {
    // 显示主窗口
    const mainWindow = BrowserWindow.getAllWindows().find(
      (w) => w !== captureWindow
    );
    if (mainWindow) {
      mainWindow.show();
    }
  }
});
